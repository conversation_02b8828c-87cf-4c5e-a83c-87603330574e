<?php
/**
 * Lo<PERSON> görünt<PERSON><PERSON><PERSON> sayfası
 */

// Doğrudan erişimi engel<PERSON>
if (!defined('ABSPATH')) {
    exit;
}

// Sayfalama parametreleri
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20;
$log_type = isset($_GET['log_type']) ? sanitize_text_field($_GET['log_type']) : 'all';

// Logları al
$order_logs = get_option('woo_pruduct_logs', array());
$button_logs = get_option('woo_pruduct_button_logs', array());

// Log tipine göre filtrele
$all_logs = array();
if ($log_type === 'all' || $log_type === 'order') {
    foreach ($order_logs as $log) {
        $log['type'] = 'order';
        $all_logs[] = $log;
    }
}
if ($log_type === 'all' || $log_type === 'button') {
    foreach ($button_logs as $log) {
        $log['type'] = 'button';
        $all_logs[] = $log;
    }
}

// Tarihe göre sırala (en yeni önce)
usort($all_logs, function($a, $b) {
    return strtotime($b['timestamp']) - strtotime($a['timestamp']);
});

// Sayfalama hesapla
$total_logs = count($all_logs);
$total_pages = ceil($total_logs / $per_page);
$offset = ($current_page - 1) * $per_page;
$current_logs = array_slice($all_logs, $offset, $per_page);

?>

<div class="wrap">
    <h1><?php _e('Sistem Logları', 'woo-pruduct'); ?></h1>
    
    <div class="woo-pruduct-logs-page">
        <!-- Log Filtreleri -->
        <div class="log-filters">
            <div class="filter-controls">
                <form method="get" action="">
                    <input type="hidden" name="page" value="woo-pruduct-logs">
                    
                    <select name="log_type" onchange="this.form.submit()">
                        <option value="all" <?php selected($log_type, 'all'); ?>><?php _e('Tüm Loglar', 'woo-pruduct'); ?></option>
                        <option value="order" <?php selected($log_type, 'order'); ?>><?php _e('Sipariş Logları', 'woo-pruduct'); ?></option>
                        <option value="button" <?php selected($log_type, 'button'); ?>><?php _e('Buton Logları', 'woo-pruduct'); ?></option>
                    </select>
                    
                    <button type="button" id="refresh-logs" class="button">
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('Yenile', 'woo-pruduct'); ?>
                    </button>
                    
                    <button type="button" id="clear-logs" class="button button-secondary">
                        <span class="dashicons dashicons-trash"></span>
                        <?php _e('Logları Temizle', 'woo-pruduct'); ?>
                    </button>
                    
                    <button type="button" id="export-logs" class="button button-secondary">
                        <span class="dashicons dashicons-download"></span>
                        <?php _e('Dışa Aktar', 'woo-pruduct'); ?>
                    </button>
                </form>
            </div>
            
            <div class="log-stats">
                <div class="stat-item">
                    <span class="stat-label"><?php _e('Toplam Log:', 'woo-pruduct'); ?></span>
                    <span class="stat-value"><?php echo esc_html($total_logs); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><?php _e('Sipariş Logları:', 'woo-pruduct'); ?></span>
                    <span class="stat-value"><?php echo esc_html(count($order_logs)); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><?php _e('Buton Logları:', 'woo-pruduct'); ?></span>
                    <span class="stat-value"><?php echo esc_html(count($button_logs)); ?></span>
                </div>
            </div>
        </div>
        
        <!-- Log Tablosu -->
        <div class="log-table-container">
            <?php if (!empty($current_logs)) : ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th class="column-timestamp"><?php _e('Tarih/Saat', 'woo-pruduct'); ?></th>
                            <th class="column-type"><?php _e('Tip', 'woo-pruduct'); ?></th>
                            <th class="column-action"><?php _e('İşlem', 'woo-pruduct'); ?></th>
                            <th class="column-details"><?php _e('Detaylar', 'woo-pruduct'); ?></th>
                            <th class="column-status"><?php _e('Durum', 'woo-pruduct'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($current_logs as $log) : ?>
                            <tr class="log-row log-type-<?php echo esc_attr($log['type']); ?>">
                                <td class="column-timestamp">
                                    <?php echo esc_html(date_i18n('d.m.Y H:i:s', strtotime($log['timestamp']))); ?>
                                </td>
                                <td class="column-type">
                                    <span class="log-type-badge log-type-<?php echo esc_attr($log['type']); ?>">
                                        <?php 
                                        if ($log['type'] === 'order') {
                                            echo '<span class="dashicons dashicons-cart"></span> ' . __('Sipariş', 'woo-pruduct');
                                        } else {
                                            echo '<span class="dashicons dashicons-button"></span> ' . __('Buton', 'woo-pruduct');
                                        }
                                        ?>
                                    </span>
                                </td>
                                <td class="column-action">
                                    <?php
                                    if ($log['type'] === 'order') {
                                        echo sprintf(
                                            __('Sipariş #%s durum değişikliği', 'woo-pruduct'),
                                            esc_html($log['order_id'])
                                        );
                                    } else {
                                        $action_text = isset($log['action']) ? $log['action'] : 'unknown';
                                        switch ($action_text) {
                                            case 'button_update':
                                                echo __('Buton durumu güncellendi', 'woo-pruduct');
                                                break;
                                            case 'manual_toggle':
                                                echo __('Manuel buton değişikliği', 'woo-pruduct');
                                                break;
                                            default:
                                                echo esc_html($action_text);
                                        }
                                    }
                                    ?>
                                </td>
                                <td class="column-details">
                                    <?php if ($log['type'] === 'order') : ?>
                                        <div class="log-details">
                                            <strong><?php _e('Ürün ID:', 'woo-pruduct'); ?></strong> <?php echo esc_html($log['product_id']); ?><br>
                                            <strong><?php _e('Kurs ID:', 'woo-pruduct'); ?></strong> <?php echo esc_html($log['course_id']); ?><br>
                                            <?php if (isset($log['old_status']) && isset($log['new_status'])) : ?>
                                                <strong><?php _e('Durum:', 'woo-pruduct'); ?></strong> 
                                                <span class="status-change">
                                                    <?php echo esc_html($log['old_status']); ?> → <?php echo esc_html($log['new_status']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    <?php else : ?>
                                        <div class="log-details">
                                            <strong><?php _e('Ürün ID:', 'woo-pruduct'); ?></strong> <?php echo esc_html($log['product_id']); ?><br>
                                            <?php if (isset($log['course_id'])) : ?>
                                                <strong><?php _e('Kurs ID:', 'woo-pruduct'); ?></strong> <?php echo esc_html($log['course_id']); ?><br>
                                            <?php endif; ?>
                                            <?php if (isset($log['toggle_action'])) : ?>
                                                <strong><?php _e('İşlem:', 'woo-pruduct'); ?></strong> <?php echo esc_html($log['toggle_action']); ?><br>
                                            <?php endif; ?>
                                            <?php if (isset($log['user_id'])) : ?>
                                                <strong><?php _e('Kullanıcı:', 'woo-pruduct'); ?></strong> <?php echo esc_html($log['user_id']); ?>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="column-status">
                                    <?php
                                    if ($log['type'] === 'order' && isset($log['new_status'])) {
                                        $status = $log['new_status'];
                                        $status_class = '';
                                        $status_text = $status;
                                        
                                        switch ($status) {
                                            case 'completed':
                                                $status_class = 'status-completed';
                                                $status_text = __('Tamamlandı', 'woo-pruduct');
                                                break;
                                            case 'processing':
                                                $status_class = 'status-processing';
                                                $status_text = __('İşleniyor', 'woo-pruduct');
                                                break;
                                            case 'pending':
                                                $status_class = 'status-pending';
                                                $status_text = __('Bekliyor', 'woo-pruduct');
                                                break;
                                            case 'cancelled':
                                                $status_class = 'status-cancelled';
                                                $status_text = __('İptal', 'woo-pruduct');
                                                break;
                                            case 'refunded':
                                                $status_class = 'status-refunded';
                                                $status_text = __('İade', 'woo-pruduct');
                                                break;
                                        }
                                        
                                        echo '<span class="status-badge ' . esc_attr($status_class) . '">' . esc_html($status_text) . '</span>';
                                    } else {
                                        echo '<span class="status-badge status-info">' . __('Bilgi', 'woo-pruduct') . '</span>';
                                    }
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- Sayfalama -->
                <?php if ($total_pages > 1) : ?>
                    <div class="tablenav bottom">
                        <div class="tablenav-pages">
                            <?php
                            $page_links = paginate_links(array(
                                'base' => add_query_arg('paged', '%#%'),
                                'format' => '',
                                'prev_text' => __('&laquo; Önceki', 'woo-pruduct'),
                                'next_text' => __('Sonraki &raquo;', 'woo-pruduct'),
                                'total' => $total_pages,
                                'current' => $current_page,
                                'type' => 'plain'
                            ));
                            
                            if ($page_links) {
                                echo '<span class="displaying-num">' . 
                                     sprintf(__('%s öğe', 'woo-pruduct'), number_format_i18n($total_logs)) . 
                                     '</span>';
                                echo $page_links;
                            }
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php else : ?>
                <div class="no-logs">
                    <span class="dashicons dashicons-info"></span>
                    <h3><?php _e('Log kaydı bulunamadı', 'woo-pruduct'); ?></h3>
                    <p><?php _e('Henüz hiçbir log kaydı oluşturulmamış veya seçilen filtreye uygun kayıt bulunmuyor.', 'woo-pruduct'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.woo-pruduct-logs-page {
    max-width: 1200px;
}

.log-filters {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-controls select {
    min-width: 150px;
}

.filter-controls .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

.log-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #0073aa;
}

.log-table-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.log-table-container table {
    margin: 0;
}

.column-timestamp {
    width: 150px;
}

.column-type {
    width: 100px;
}

.column-action {
    width: 200px;
}

.column-details {
    width: auto;
}

.column-status {
    width: 120px;
}

.log-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.log-type-order {
    background: #e1f5fe;
    color: #0277bd;
}

.log-type-button {
    background: #f3e5f5;
    color: #7b1fa2;
}

.log-details {
    font-size: 12px;
    line-height: 1.4;
}

.status-change {
    font-family: monospace;
    background: #f0f0f0;
    padding: 2px 4px;
    border-radius: 3px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-completed {
    background: #46b450;
    color: #fff;
}

.status-processing {
    background: #00a0d2;
    color: #fff;
}

.status-pending {
    background: #ffb900;
    color: #fff;
}

.status-cancelled {
    background: #dc3232;
    color: #fff;
}

.status-refunded {
    background: #ff6900;
    color: #fff;
}

.status-info {
    background: #666;
    color: #fff;
}

.no-logs {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-logs .dashicons {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-logs h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
}

.no-logs p {
    margin: 0;
    font-size: 14px;
}

.tablenav {
    padding: 15px 20px;
    background: #f9f9f9;
    border-top: 1px solid #ddd;
}

.tablenav-pages {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.displaying-num {
    color: #666;
    font-size: 14px;
}

@media (max-width: 768px) {
    .log-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-controls {
        justify-content: center;
    }
    
    .log-stats {
        justify-content: space-around;
    }
    
    .log-table-container {
        overflow-x: auto;
    }
    
    .log-table-container table {
        min-width: 800px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    $('#refresh-logs').on('click', function() {
        location.reload();
    });
    
    $('#clear-logs').on('click', function() {
        if (confirm('<?php _e("Tüm logları silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "woo-pruduct"); ?>')) {
            $.post(ajaxurl, {
                action: 'woo_pruduct_clear_logs',
                nonce: '<?php echo wp_create_nonce("woo_pruduct_nonce"); ?>'
            }, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('<?php _e("Loglar temizlenirken bir hata oluştu.", "woo-pruduct"); ?>');
                }
            });
        }
    });
    
    $('#export-logs').on('click', function() {
        // CSV formatında logları dışa aktar
        let csv = 'Tarih,Tip,İşlem,Ürün ID,Kurs ID,Durum\n';
        
        $('.log-row').each(function() {
            const $row = $(this);
            const timestamp = $row.find('.column-timestamp').text().trim();
            const type = $row.find('.log-type-badge').text().trim();
            const action = $row.find('.column-action').text().trim();
            const details = $row.find('.column-details').text().trim().replace(/\n/g, ' ');
            const status = $row.find('.status-badge').text().trim();
            
            csv += `"${timestamp}","${type}","${action}","${details}","${status}"\n`;
        });
        
        // Dosyayı indir
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'woo-pruduct-logs.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
});
</script>
