/**
 * Woo Product Manager Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Global değişkenler
    let scanResults = [];
    let isScanning = false;
    
    $(document).ready(function() {
        initializeAdmin();
    });
    
    /**
     * Admin panelini başlat
     */
    function initializeAdmin() {
        // Dashboard işlemleri
        initializeDashboard();
        
        // Tarama sayfası işlemleri
        initializeScanPage();
        
        // Log sayfası işlemleri
        initializeLogsPage();
        
        // Genel işlemler
        initializeGeneral();
    }
    
    /**
     * Dashboard işlemlerini başlat
     */
    function initializeDashboard() {
        // İstatistikleri yenile
        $('#refresh-stats').on('click', function() {
            refreshStats();
        });
        
        // Logları temizle
        $('#clear-logs').on('click', function() {
            clearLogs();
        });
    }
    
    /**
     * Tarama sayfası işlemlerini başlat
     */
    function initializeScanPage() {
        // <PERSON><PERSON>yı başlat
        $('#start-scan').on('click', function() {
            if (!isScanning) {
                startScan();
            }
        });
        
        // Sonuçları dışa aktar
        $('#export-results').on('click', function() {
            exportResults();
        });
        
        // Detay görüntüleme
        $(document).on('click', '.view-details', function() {
            const productId = $(this).data('product-id');
            showProductDetails(productId);
        });
        
        // Durum değiştirme
        $(document).on('click', '.toggle-status', function() {
            const productId = $(this).data('product-id');
            toggleProductStatus(productId);
        });
    }
    
    /**
     * Log sayfası işlemlerini başlat
     */
    function initializeLogsPage() {
        // Logları yenile
        $('#refresh-logs').on('click', function() {
            refreshLogs();
        });
        
        // Logları temizle
        $('#clear-logs').on('click', function() {
            clearAllLogs();
        });
        
        // Logları dışa aktar
        $('#export-logs').on('click', function() {
            exportLogs();
        });
    }
    
    /**
     * Genel işlemleri başlat
     */
    function initializeGeneral() {
        // Tooltip'leri başlat
        initializeTooltips();
        
        // Klavye kısayolları
        initializeKeyboardShortcuts();
        
        // Auto-refresh
        initializeAutoRefresh();
    }
    
    /**
     * İstatistikleri yenile
     */
    function refreshStats() {
        showLoading('#refresh-stats');
        
        setTimeout(function() {
            location.reload();
        }, 500);
    }
    
    /**
     * Logları temizle
     */
    function clearLogs() {
        if (!confirm(wooPruductAjax.strings.confirmClearLogs || 'Tüm logları silmek istediğinizden emin misiniz?')) {
            return;
        }
        
        showLoading('#clear-logs');
        
        $.ajax({
            url: wooPruductAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'woo_pruduct_clear_logs',
                nonce: wooPruductAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showSuccessMessage('Loglar başarıyla temizlendi.');
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showErrorMessage(response.data || 'Loglar temizlenirken bir hata oluştu.');
                }
            },
            error: function() {
                showErrorMessage('Sunucu hatası oluştu.');
            },
            complete: function() {
                hideLoading('#clear-logs');
            }
        });
    }
    
    /**
     * Taramayı başlat
     */
    function startScan() {
        if (isScanning) {
            return;
        }
        
        isScanning = true;
        const $button = $('#start-scan');
        const $progress = $('#scan-progress');
        const $results = $('#scan-results');
        
        // UI'yi güncelle
        showLoading($button);
        $progress.show();
        $results.hide();
        $('#export-results').hide();
        
        // Progress bar animasyonu
        animateProgress();
        
        // AJAX isteği
        $.ajax({
            url: wooPruductAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'woo_pruduct_scan',
                nonce: wooPruductAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    scanResults = response.data;
                    displayScanResults(scanResults);
                    showSuccessMessage('Tarama başarıyla tamamlandı!');
                    $('#export-results').show();
                } else {
                    showErrorMessage(response.data || 'Tarama sırasında bir hata oluştu.');
                }
            },
            error: function() {
                showErrorMessage('Sunucu hatası oluştu.');
            },
            complete: function() {
                isScanning = false;
                hideLoading($button);
                $progress.hide();
                $(document).trigger('scanComplete');
            }
        });
    }
    
    /**
     * Progress bar animasyonu
     */
    function animateProgress() {
        const $fill = $('.progress-fill');
        const $text = $('.progress-text');
        
        let progress = 0;
        const messages = [
            'Tarama başlatılıyor...',
            'Ürünler taranıyor...',
            'Kurs bağlantıları kontrol ediliyor...',
            'Siparişler analiz ediliyor...',
            'Sonuçlar hazırlanıyor...'
        ];
        
        const interval = setInterval(function() {
            if (!isScanning) {
                clearInterval(interval);
                return;
            }
            
            progress += Math.random() * 10;
            if (progress > 90) {
                progress = 90;
            }
            
            $fill.css('width', progress + '%');
            
            const messageIndex = Math.floor((progress / 100) * messages.length);
            if (messages[messageIndex]) {
                $text.text(messages[messageIndex]);
            }
        }, 300);
        
        // Tarama tamamlandığında
        $(document).one('scanComplete', function() {
            clearInterval(interval);
            $fill.css('width', '100%');
            $text.text('Tarama tamamlandı!');
        });
    }
    
    /**
     * Tarama sonuçlarını göster
     */
    function displayScanResults(results) {
        const $results = $('#scan-results');
        const $tbody = $('#results-tbody');
        
        // Özet istatistikleri güncelle
        updateScanSummary(results);
        
        // Tablo içeriğini temizle
        $tbody.empty();
        
        // Sonuçları tabloya ekle
        results.forEach(function(item) {
            const row = createResultRow(item);
            $tbody.append(row);
        });
        
        // Sonuçları göster
        $results.show();
        
        // Tablo sıralamasını etkinleştir
        initializeTableSorting();
    }
    
    /**
     * Tarama özetini güncelle
     */
    function updateScanSummary(results) {
        let totalOrders = 0;
        let completedOrders = 0;
        
        results.forEach(function(item) {
            totalOrders += item.orders_count || 0;
            completedOrders += item.completed_orders || 0;
        });
        
        $('#total-products').text(results.length);
        $('#total-orders').text(totalOrders);
        $('#completed-orders').text(completedOrders);
    }
    
    /**
     * Sonuç satırı oluştur
     */
    function createResultRow(item) {
        const completionRate = item.orders_count > 0 ? 
            Math.round((item.completed_orders / item.orders_count) * 100) : 0;
        
        let statusClass = 'status-inactive';
        let statusText = 'Pasif';
        
        if (completionRate > 70) {
            statusClass = 'status-active';
            statusText = 'Aktif';
        } else if (completionRate > 30) {
            statusClass = 'status-partial';
            statusText = 'Kısmi';
        }
        
        return `
            <tr data-product-id="${item.product_id}">
                <td>
                    <strong>${escapeHtml(item.course_title)}</strong><br>
                    <small>ID: ${item.course_id}</small>
                </td>
                <td>
                    <strong>${escapeHtml(item.product_title)}</strong><br>
                    <small>ID: ${item.product_id}</small>
                </td>
                <td>${item.product_price ? formatPrice(item.product_price) : '-'}</td>
                <td>${item.orders_count || 0}</td>
                <td>${item.completed_orders || 0} (${completionRate}%)</td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="button button-small view-details" data-product-id="${item.product_id}">
                            Detay
                        </button>
                        <button class="button button-small toggle-status" data-product-id="${item.product_id}">
                            Değiştir
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
    
    /**
     * Ürün detaylarını göster
     */
    function showProductDetails(productId) {
        const product = scanResults.find(item => item.product_id == productId);
        
        if (!product) {
            showErrorMessage('Ürün bulunamadı.');
            return;
        }
        
        let detailsHtml = `
            <div class="product-details">
                <h3>${escapeHtml(product.course_title)}</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <strong>Ürün:</strong> ${escapeHtml(product.product_title)}
                    </div>
                    <div class="detail-item">
                        <strong>Fiyat:</strong> ${formatPrice(product.product_price)}
                    </div>
                    <div class="detail-item">
                        <strong>Toplam Sipariş:</strong> ${product.orders_count || 0}
                    </div>
                    <div class="detail-item">
                        <strong>Tamamlanmış Sipariş:</strong> ${product.completed_orders || 0}
                    </div>
                </div>
        `;
        
        if (product.orders && product.orders.length > 0) {
            detailsHtml += '<h4>Son Siparişler:</h4><ul class="order-list">';
            product.orders.slice(0, 5).forEach(function(order) {
                detailsHtml += `
                    <li class="order-item">
                        <span class="order-id">Sipariş #${order.order_id}</span>
                        <span class="order-status status-badge status-${order.order_status.replace('wc-', '')}">${order.order_status}</span>
                        <span class="order-date">${formatDate(order.post_date)}</span>
                    </li>
                `;
            });
            detailsHtml += '</ul>';
        }
        
        detailsHtml += '</div>';
        
        $('#analysis-content').html(detailsHtml);
        $('#detailed-analysis').show();
        
        // Detay bölümüne scroll
        $('html, body').animate({
            scrollTop: $('#detailed-analysis').offset().top - 50
        }, 500);
    }
    
    /**
     * Ürün durumunu değiştir
     */
    function toggleProductStatus(productId) {
        if (!confirm('Bu ürünün durumunu değiştirmek istediğinizden emin misiniz?')) {
            return;
        }
        
        const $button = $(`.toggle-status[data-product-id="${productId}"]`);
        showLoading($button);
        
        $.ajax({
            url: wooPruductAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'woo_pruduct_toggle_button',
                product_id: productId,
                action_type: 'toggle',
                nonce: wooPruductAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showSuccessMessage('Durum başarıyla değiştirildi.');
                    // Satırı güncelle
                    updateProductRow(productId);
                } else {
                    showErrorMessage(response.data || 'Durum değiştirilemedi.');
                }
            },
            error: function() {
                showErrorMessage('Sunucu hatası oluştu.');
            },
            complete: function() {
                hideLoading($button);
            }
        });
    }
    
    /**
     * Sonuçları dışa aktar
     */
    function exportResults() {
        if (scanResults.length === 0) {
            showErrorMessage('Dışa aktarılacak veri bulunamadı.');
            return;
        }
        
        // CSV formatında dışa aktar
        let csv = 'Kurs ID,Kurs Adı,Ürün ID,Ürün Adı,Fiyat,Sipariş Sayısı,Tamamlanmış Sipariş,Tamamlanma Oranı\n';
        
        scanResults.forEach(function(item) {
            const completionRate = item.orders_count > 0 ? 
                Math.round((item.completed_orders / item.orders_count) * 100) : 0;
            
            csv += `${item.course_id},"${escapeCSV(item.course_title)}",${item.product_id},"${escapeCSV(item.product_title)}",${item.product_price || 0},${item.orders_count || 0},${item.completed_orders || 0},${completionRate}%\n`;
        });
        
        downloadCSV(csv, 'woo-pruduct-scan-results.csv');
        showSuccessMessage('Sonuçlar başarıyla dışa aktarıldı.');
    }
    
    /**
     * Logları yenile
     */
    function refreshLogs() {
        location.reload();
    }
    
    /**
     * Tüm logları temizle
     */
    function clearAllLogs() {
        if (!confirm('Tüm logları silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
            return;
        }
        
        clearLogs();
    }
    
    /**
     * Logları dışa aktar
     */
    function exportLogs() {
        const rows = $('.log-row');
        if (rows.length === 0) {
            showErrorMessage('Dışa aktarılacak log bulunamadı.');
            return;
        }
        
        let csv = 'Tarih,Tip,İşlem,Detaylar,Durum\n';
        
        rows.each(function() {
            const $row = $(this);
            const timestamp = $row.find('.column-timestamp').text().trim();
            const type = $row.find('.log-type-badge').text().trim();
            const action = $row.find('.column-action').text().trim();
            const details = $row.find('.column-details').text().trim().replace(/\n/g, ' ');
            const status = $row.find('.status-badge').text().trim();
            
            csv += `"${escapeCSV(timestamp)}","${escapeCSV(type)}","${escapeCSV(action)}","${escapeCSV(details)}","${escapeCSV(status)}"\n`;
        });
        
        downloadCSV(csv, 'woo-pruduct-logs.csv');
        showSuccessMessage('Loglar başarıyla dışa aktarıldı.');
    }
    
    /**
     * Tooltip'leri başlat
     */
    function initializeTooltips() {
        $('[data-tooltip]').each(function() {
            $(this).attr('title', $(this).data('tooltip'));
        });
    }
    
    /**
     * Klavye kısayollarını başlat
     */
    function initializeKeyboardShortcuts() {
        $(document).on('keydown', function(e) {
            // Ctrl+R: Yenile
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                location.reload();
            }
            
            // Ctrl+S: Taramayı başlat (tarama sayfasında)
            if (e.ctrlKey && e.key === 's' && $('#start-scan').length) {
                e.preventDefault();
                $('#start-scan').click();
            }
        });
    }
    
    /**
     * Auto-refresh'i başlat
     */
    function initializeAutoRefresh() {
        // Dashboard'da 5 dakikada bir yenile
        if ($('.woo-pruduct-dashboard').length) {
            setInterval(function() {
                refreshStats();
            }, 300000); // 5 dakika
        }
    }
    
    /**
     * Tablo sıralamasını başlat
     */
    function initializeTableSorting() {
        $('.wp-list-table th').on('click', function() {
            const $table = $(this).closest('table');
            const columnIndex = $(this).index();
            const $tbody = $table.find('tbody');
            const rows = $tbody.find('tr').toArray();
            
            const isAscending = !$(this).hasClass('sorted-asc');
            
            // Sıralama sınıflarını temizle
            $table.find('th').removeClass('sorted-asc sorted-desc');
            $(this).addClass(isAscending ? 'sorted-asc' : 'sorted-desc');
            
            // Satırları sırala
            rows.sort(function(a, b) {
                const aText = $(a).find('td').eq(columnIndex).text().trim();
                const bText = $(b).find('td').eq(columnIndex).text().trim();
                
                // Sayısal değerleri kontrol et
                const aNum = parseFloat(aText.replace(/[^\d.-]/g, ''));
                const bNum = parseFloat(bText.replace(/[^\d.-]/g, ''));
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return isAscending ? aNum - bNum : bNum - aNum;
                }
                
                // Metin sıralaması
                return isAscending ? 
                    aText.localeCompare(bText) : 
                    bText.localeCompare(aText);
            });
            
            // Sıralanmış satırları tabloya ekle
            $tbody.empty().append(rows);
        });
    }
    
    /**
     * Ürün satırını güncelle
     */
    function updateProductRow(productId) {
        const $row = $(`tr[data-product-id="${productId}"]`);
        if ($row.length) {
            $row.addClass('updated');
            setTimeout(function() {
                $row.removeClass('updated');
            }, 2000);
        }
    }
    
    /**
     * Loading durumunu göster
     */
    function showLoading(element) {
        $(element).addClass('loading').prop('disabled', true);
    }
    
    /**
     * Loading durumunu gizle
     */
    function hideLoading(element) {
        $(element).removeClass('loading').prop('disabled', false);
    }
    
    /**
     * Başarı mesajı göster
     */
    function showSuccessMessage(message) {
        showMessage(message, 'success');
    }
    
    /**
     * Hata mesajı göster
     */
    function showErrorMessage(message) {
        showMessage(message, 'error');
    }
    
    /**
     * Uyarı mesajı göster
     */
    function showWarningMessage(message) {
        showMessage(message, 'warning');
    }
    
    /**
     * Mesaj göster
     */
    function showMessage(message, type) {
        const $message = $(`<div class="${type}-message">${message}</div>`);
        $('.wrap').prepend($message);
        
        setTimeout(function() {
            $message.fadeOut(function() {
                $message.remove();
            });
        }, 5000);
    }
    
    /**
     * HTML escape
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * CSV escape
     */
    function escapeCSV(text) {
        return String(text).replace(/"/g, '""');
    }
    
    /**
     * Fiyat formatla
     */
    function formatPrice(price) {
        return parseFloat(price).toFixed(2) + ' ₺';
    }
    
    /**
     * Tarih formatla
     */
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('tr-TR');
    }
    
    /**
     * CSV dosyası indir
     */
    function downloadCSV(csv, filename) {
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
})(jQuery);
